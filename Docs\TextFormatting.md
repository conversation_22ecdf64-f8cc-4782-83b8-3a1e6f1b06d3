# Text Formatting Features

This document describes the text formatting features added to the AI Notepad application, providing common word processing controls similar to Microsoft Word.

## Overview

The text formatting features are located in the sidebar and provide users with common text formatting options organized in two rows:

- **Row 1**: Text alignment controls (Left, Center, Right, Justify)
- **Row 2**: Line spacing selection

## Features

### Text Alignment

The text alignment feature provides four options:

1. **Left Align** (⬅️) - Default alignment, text aligned to the left margin
2. **Center Align** (↔️) - Text centered horizontally
3. **Right Align** (➡️) - Text aligned to the right margin  
4. **Justify** (⬌) - Text aligned to both left and right margins

#### Implementation Details

- **Type**: `TextAlignType = 'left' | 'center' | 'right' | 'justify'`
- **Default**: `'left'`
- **Storage**: Persisted in localStorage as part of unified settings
- **UI**: Button group with icon indicators for each alignment option

### Line Spacing

The line spacing feature provides six preset options:

1. **Single** (1.0) - Standard single line spacing
2. **1.15** - Slightly increased spacing for better readability
3. **1.5** - One and a half line spacing (default)
4. **Double** (2.0) - Double line spacing
5. **2.5** - Two and a half line spacing
6. **Triple** (3.0) - Triple line spacing

#### Implementation Details

- **Type**: `LineSpacingType = '1.0' | '1.15' | '1.5' | '2.0' | '2.5' | '3.0'`
- **Default**: `'1.5'`
- **Storage**: Persisted in localStorage as part of unified settings
- **UI**: Dropdown select with descriptive names

## User Interface

### Layout

The text formatting controls are positioned in the sidebar after the font settings:

```
Settings
├── Font Family
├── Font Size
├── Text Formatting:
│   ├── Row 1: [⬅️] [↔️] [➡️] [⬌] (Alignment buttons)
│   └── Row 2: Line Spacing: [Dropdown]
├── Thinking Panel Toggle
└── AI Configuration
```

### Visual Design

- **Alignment Buttons**: Grouped together with border, active state highlighting
- **Line Spacing**: Standard dropdown with clear option names
- **Responsive**: Adapts to sidebar width with proper spacing
- **Accessibility**: Proper labels and keyboard navigation support

## Technical Implementation

### Type Definitions

```typescript
// Text alignment options
export type TextAlignType = 'left' | 'center' | 'right' | 'justify';

// Line spacing options  
export type LineSpacingType = '1.0' | '1.15' | '1.5' | '2.0' | '2.5' | '3.0';

// Updated AppSettings interface
export interface AppSettings {
    // ... existing properties
    textAlign: TextAlignType;
    lineSpacing: LineSpacingType;
    // ... other properties
}
```

### Default Settings

```typescript
export const DEFAULT_APP_SETTINGS: AppSettings = {
    // ... existing defaults
    textAlign: 'left',
    lineSpacing: '1.5',
    // ... other defaults
};
```

### Application to Text

The formatting settings are applied directly to the textarea element via inline styles:

```typescript
style="font-family: {$appSettings.fontFamily}; 
       font-size: {$appSettings.fontSize}px; 
       text-align: {$appSettings.textAlign}; 
       line-height: {$appSettings.lineSpacing};"
```

## Persistence

All text formatting settings are automatically saved to localStorage as part of the unified settings system:

- **Auto-save**: Changes are saved immediately when modified
- **Persistence**: Settings persist across browser sessions
- **Restoration**: Settings are restored on page load
- **Integration**: Works seamlessly with existing settings system

## Testing

Comprehensive test coverage includes:

- Default value verification
- Setting updates and persistence
- Integration with existing settings
- Multiple sequential changes
- Settings isolation (changes don't affect other settings)

Test file: `tests/textFormatting.test.ts`

## Browser Compatibility

The text formatting features use standard CSS properties:

- `text-align`: Supported in all modern browsers
- `line-height`: Supported in all modern browsers
- No special polyfills or fallbacks required

## Future Enhancements

Potential future additions could include:

- Font weight (bold/normal)
- Font style (italic/normal)
- Text decoration (underline, strikethrough)
- Text color selection
- Background color
- Custom line spacing values
- Paragraph spacing controls

## Usage Examples

### Programmatic Usage

```typescript
import { updateAppSetting } from '$lib/stores/unifiedSettingsStore';

// Change text alignment
updateAppSetting('textAlign', 'center');

// Change line spacing
updateAppSetting('lineSpacing', '2.0');
```

### User Workflow

1. User opens the sidebar
2. Locates "Text Formatting" section
3. Clicks desired alignment button (⬅️, ↔️, ➡️, ⬌)
4. Selects line spacing from dropdown
5. Changes are immediately applied to the notepad
6. Settings are automatically saved

The text formatting features provide a familiar, intuitive interface for users coming from traditional word processors while maintaining the clean, modern design of the AI Notepad application.
